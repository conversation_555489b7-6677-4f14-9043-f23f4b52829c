import 'package:dio/dio.dart';
import 'interceptors/logging_interceptor.dart';

class SiliconFlowDioClient {
  final Dio _dio;

  SiliconFlowDioClient() : _dio = Dio() {
    _dio.options = BaseOptions(
      baseUrl: 'https://api.siliconflow.cn/v1',
      connectTimeout: const Duration(seconds: 10),
      receiveTimeout: const Duration(seconds: 15),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    );

    // 只在 Debug 模式下添加日志拦截器
    assert(() {
      _dio.interceptors.add(LoggingInterceptor());
      return true;
    }());

    // 添加 SiliconFlow 认证拦截器
    _dio.interceptors.add(SiliconFlowAuthInterceptor());
  }

  Dio get instance => _dio;
}

/// SiliconFlow 认证拦截器
class SiliconFlowAuthInterceptor extends Interceptor {
  @override
  void onRequest(
      RequestOptions options, RequestInterceptorHandler handler) async {
    // 从存储服务获取 API Key
    // 这里需要导入 storage_service，但为了解耦，我们暂时通过请求头传递
    // 实际使用时应该在初始化时设置 API Key

    // 如果请求头中已经有 Authorization，直接使用
    if (options.headers['Authorization'] == null) {
      // 可以在这里添加默认的 API Key 或者抛出异常
      handler.reject(
        DioException(
          requestOptions: options,
          error: 'API Key 未设置，请先配置 SiliconFlow API Key',
        ),
      );
      return;
    }

    handler.next(options);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    // 处理认证错误
    if (err.response?.statusCode == 401) {
      handler.reject(
        DioException(
          requestOptions: err.requestOptions,
          error: 'API Key 无效或已过期，请检查配置',
          response: err.response,
        ),
      );
      return;
    }

    handler.next(err);
  }
}
