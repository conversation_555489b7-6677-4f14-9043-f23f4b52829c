import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../../core/design/app_design_system.dart';
import 'webview_page.dart';
import 'language_settings_page.dart';

class ManagementPage extends StatelessWidget {
  const ManagementPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.background,
        elevation: 0,
        title: Text(
          AppLocalizations.of(context)!.management_page_title,
          style: AppTypography.headlineLarge,
        ),
        centerTitle: false,
      ),
      body: Padding(
        padding:
            const EdgeInsets.symmetric(horizontal: AppSpacing.screenPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: AppSpacing.md),
            // 用户资料卡片
            _buildProfileCard(context),
            const SizedBox(height: AppSpacing.lg),
            // 设置列表
            _buildSettingsList(context),
            const Spacer(),
            // 版本信息
            _buildVersionInfo(context),
            const SizedBox(height: AppSpacing.xl),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileCard(BuildContext context) {
    return AppCard(
      backgroundColor: AppColors.surface,
      padding: const EdgeInsets.all(AppSpacing.md),
      child: Row(
        children: [
          // 头像
          CircleAvatar(
            radius: 30,
            backgroundColor: AppColors.primary,
            child: Text(
              AppLocalizations.of(context)!.user_avatar_placeholder,
              style: GoogleFonts.poppins(
                fontSize: 20,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
            ),
          ),
          const SizedBox(width: AppSpacing.md),
          // 用户信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  AppLocalizations.of(context)!.user_name_placeholder,
                  style: AppTypography.titleLarge,
                ),
                const SizedBox(height: AppSpacing.xs),
                Text(
                  AppLocalizations.of(context)!.user_email_placeholder,
                  style: AppTypography.bodyMedium,
                ),
              ],
            ),
          ),
          // 编辑按钮
          AppIconButton(
            icon: Icons.edit_outlined,
            onPressed: () {
              // TODO: 导航到个人资料编辑页面
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                    content: Text(AppLocalizations.of(context)!
                        .message_feature_in_development)),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsList(BuildContext context) {
    return Column(
      children: [
        _buildSettingsItem(
          context,
          icon: Icons.language_outlined,
          title: AppLocalizations.of(context)!.settings_language,
          subtitle: AppLocalizations.of(context)!
              .settings_language_simplified_chinese,
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const LanguageSettingsPage(),
              ),
            );
          },
        ),
        const Divider(color: AppColors.border, height: 1),
        _buildSettingsItem(
          context,
          icon: Icons.description_outlined,
          title: AppLocalizations.of(context)!.settings_terms_of_service,
          onTap: () =>
              _openWebView(context, '使用条款', 'https://your-app.com/terms'),
        ),
        const Divider(color: AppColors.border, height: 1),
        _buildSettingsItem(
          context,
          icon: Icons.privacy_tip_outlined,
          title: AppLocalizations.of(context)!.settings_privacy_policy,
          onTap: () =>
              _openWebView(context, '隐私政策', 'https://your-app.com/privacy'),
        ),
      ],
    );
  }

  Widget _buildSettingsItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    String? subtitle,
    required VoidCallback onTap,
  }) {
    return ListTile(
      contentPadding: EdgeInsets.zero,
      leading: Icon(
        icon,
        size: 24,
        color: AppColors.textSecondary,
      ),
      title: Text(
        title,
        style: GoogleFonts.poppins(
          fontSize: 16,
          fontWeight: FontWeight.w400,
          color: AppColors.textPrimary,
        ),
      ),
      subtitle: subtitle != null
          ? Text(
              subtitle,
              style: AppTypography.bodyMedium,
            )
          : null,
      trailing: const Icon(
        Icons.chevron_right,
        color: AppColors.textSecondary,
      ),
      onTap: onTap,
    );
  }

  Widget _buildVersionInfo(BuildContext context) {
    return Center(
      child: Text(
        AppLocalizations.of(context)!.app_version, // 从pubspec.yaml读取的版本号
        style: AppTypography.bodyMedium,
      ),
    );
  }

  void _openWebView(BuildContext context, String title, String url) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => WebViewPage(
          title: title,
          url: url,
        ),
      ),
    );
  }
}
