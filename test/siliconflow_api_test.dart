import 'package:flutter_test/flutter_test.dart';

import 'package:round_table/data/models/chat_message.dart';
import 'package:round_table/data/services/siliconflow_api_service.dart';
import 'package:round_table/data/repositories/chat_repository.dart';
import 'package:round_table/core/network/api_exception.dart';

void main() {
  group('SiliconFlow API Tests', () {
    test('ChatMessage model creation', () {
      const message = ChatMessage(
        role: 'user',
        content: 'Hello, AI!',
      );

      expect(message.role, 'user');
      expect(message.content, 'Hello, AI!');
    });

    test('ChatCompletionRequest model creation', () {
      final request = ChatCompletionRequest(
        model: 'deepseek-ai/DeepSeek-V2.5',
        messages: const [
          ChatMessage(role: 'user', content: 'Hello'),
        ],
        maxTokens: 2048,
        thinkingBudget: 1024,
      );

      expect(request.model, 'deepseek-ai/DeepSeek-V2.5');
      expect(request.messages.length, 1);
      expect(request.maxTokens, 2048);
      expect(request.thinkingBudget, 1024);
      expect(request.stream, false);
    });

    test('ChatCompletionResponse model parsing', () {
      final json = {
        'id': 'chatcmpl-123',
        'object': 'chat.completion',
        'created': 1234567890,
        'model': 'deepseek-ai/DeepSeek-V2.5',
        'choices': [
          {
            'index': 0,
            'message': {
              'role': 'assistant',
              'content': 'Hello! How can I help you?',
            },
            'finish_reason': 'stop',
          },
        ],
        'usage': {
          'prompt_tokens': 10,
          'completion_tokens': 8,
          'total_tokens': 18,
        },
      };

      final response = ChatCompletionResponse.fromJson(json);

      expect(response.id, 'chatcmpl-123');
      expect(response.object, 'chat.completion');
      expect(response.model, 'deepseek-ai/DeepSeek-V2.5');
      expect(response.choices.length, 1);
      expect(response.choices[0].message.content, 'Hello! How can I help you?');
      expect(response.usage.promptTokens, 10);
      expect(response.usage.completionTokens, 8);
      expect(response.usage.totalTokens, 18);
    });

    test('StreamingDelta model parsing', () {
      final json = {
        'role': 'assistant',
        'content': 'Hello',
        'reasoning_content': 'Let me think',
      };

      final delta = StreamingDelta.fromJson(json);

      expect(delta.role, 'assistant');
      expect(delta.content, 'Hello');
      expect(delta.reasoningContent, 'Let me think');
    });

    test('API Exception hierarchy', () {
      final networkError = NetworkError();
      expect(networkError.message, '网络连接失败，请检查网络设置');

      final serverError = ServerError(statusCode: 500);
      expect(serverError.message, '服务器内部错误，请稍后重试');
      expect(serverError.statusCode, 500);

      final unauthorizedError = UnauthorizedError();
      expect(unauthorizedError.statusCode, 401);
      expect(unauthorizedError.message, '认证失败，请重新登录');

      final timeoutError = TimeoutError();
      expect(timeoutError.message, '请求超时，请重试');

      final unknownError = UnknownError(message: 'Custom error');
      expect(unknownError.message, 'Custom error');
    });
  });

  group('ChatRepository Tests', () {
    test('ChatRepository can be instantiated', () {
      final apiService = SiliconFlowApiService();
      final chatRepository = ChatRepository(apiService);

      expect(chatRepository, isA<ChatRepository>());
    });

    test('setApiKey method exists', () {
      final apiService = SiliconFlowApiService();
      final chatRepository = ChatRepository(apiService);

      // 这个方法应该存在且不抛出异常
      expect(() => chatRepository.setApiKey('test-api-key'), returnsNormally);
    });
  });
}
